[09-12 08:50:05.691] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 08:50:05.691] [debug] =====算法库初始化开始=====
[09-12 08:50:05.691] [info] Initializing unified resource manager...
[09-12 08:50:05.711] [info] Initializing GPU resource manager...
[09-12 08:50:06.099] [debug] CUDA stream created: 0x5555560687f0
[09-12 08:50:06.099] [info] GPU resource manager initialized successfully
[09-12 08:50:06.099] [info] Initializing memory pool manager...
[09-12 08:50:06.099] [info] CUDA memory pool created with max size: 512MB
[09-12 08:50:06.100] [info] Host memory pool created with max size: 1024MB
[09-12 08:50:06.100] [info] Memory pool manager initialized successfully
[09-12 08:50:06.100] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 08:50:06.100] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 08:50:06.100] [debug] Initializing resource pool for length: 512
[09-12 08:50:06.100] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 08:50:06.101] [debug] Initializing resource pool for length: 256
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.102] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 08:50:06.102] [debug] Initializing resource pool for length: 128
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 08:50:06.102] [debug] Initializing resource pool for length: 64
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 08:50:06.102] [info] All FFT resource pools initialized successfully
[09-12 08:50:06.102] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 08:50:06.102] [info] Unified resource manager initialized successfully
[09-12 08:50:06.102] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 08:50:06.103] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 08:50:06.103] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 08:50:06.127] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 08:50:06.127] [info] Engine info: 2 bindings
[09-12 08:50:06.127] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 08:50:06.127] [info]   Binding 1: output [1x1x512x1024]
[09-12 08:50:06.127] [info] 模型输入维度: [1x1024x1024x2]
[09-12 08:50:06.128] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 08:50:06.128] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 08:50:06.128] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 08:50:06.131] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 08:50:06.165] [debug] - 显存 - 算法初始化完成: 使用 1021.9 MB
[09-12 08:50:06.165] [debug] - 内存 - 算法初始化完成: 使用 259.5 MB
[09-12 08:50:06.165] [debug] =====算法库初始化完成=====
[09-12 08:50:06.844] [debug] =====开始目标检测=====
[09-12 08:50:06.844] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.584] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 08:50:07.584] [debug] =====开始目标跟踪======
[09-12 08:50:07.584] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.584] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.584] [debug] 跟踪结果数量: 0
[09-12 08:50:07.584] [debug] 开始释放检测结果数组
[09-12 08:50:07.584] [debug] 结束释放检测结果数组
[09-12 08:50:07.584] [debug] 开始释放列数据段
[09-12 08:50:07.584] [debug] 结束释放列数据段
[09-12 08:50:07.584] [debug] 已释放内部检测数据内存
[09-12 08:50:07.586] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.586] [debug] - 内存 - 目标跟踪完成: 使用 1037.7 MB
[09-12 08:50:07.586] [debug] =====开始目标检测=====
[09-12 08:50:07.586] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.594] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 08:50:07.595] [debug] =====开始目标跟踪======
[09-12 08:50:07.595] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.595] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.595] [debug] 跟踪结果数量: 0
[09-12 08:50:07.595] [debug] 开始释放检测结果数组
[09-12 08:50:07.595] [debug] 结束释放检测结果数组
[09-12 08:50:07.595] [debug] 开始释放列数据段
[09-12 08:50:07.595] [debug] 结束释放列数据段
[09-12 08:50:07.595] [debug] 已释放内部检测数据内存
[09-12 08:50:07.595] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.595] [debug] - 内存 - 目标跟踪完成: 使用 999.9 MB
[09-12 08:50:07.597] [debug] =====开始目标检测=====
[09-12 08:50:07.597] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.606] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 08:50:07.606] [debug] =====开始目标跟踪======
[09-12 08:50:07.606] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.607] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.607] [debug] 跟踪结果数量: 0
[09-12 08:50:07.607] [debug] 开始释放检测结果数组
[09-12 08:50:07.607] [debug] 结束释放检测结果数组
[09-12 08:50:07.607] [debug] 开始释放列数据段
[09-12 08:50:07.607] [debug] 结束释放列数据段
[09-12 08:50:07.607] [debug] 已释放内部检测数据内存
[09-12 08:50:07.607] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.607] [debug] - 内存 - 目标跟踪完成: 使用 923.7 MB
[09-12 08:50:07.610] [debug] =====开始目标检测=====
[09-12 08:50:07.610] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.618] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 08:50:07.618] [debug] =====开始目标跟踪======
[09-12 08:50:07.618] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.618] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.618] [debug] 跟踪结果数量: 0
[09-12 08:50:07.618] [debug] 开始释放检测结果数组
[09-12 08:50:07.618] [debug] 结束释放检测结果数组
[09-12 08:50:07.618] [debug] 开始释放列数据段
[09-12 08:50:07.618] [debug] 结束释放列数据段
[09-12 08:50:07.618] [debug] 已释放内部检测数据内存
[09-12 08:50:07.618] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.618] [debug] - 内存 - 目标跟踪完成: 使用 835.9 MB
[09-12 08:50:07.621] [debug] =====开始目标检测=====
[09-12 08:50:07.621] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.630] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 08:50:07.631] [debug] =====开始目标跟踪======
[09-12 08:50:07.631] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.631] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.631] [debug] 跟踪结果数量: 0
[09-12 08:50:07.631] [debug] 开始释放检测结果数组
[09-12 08:50:07.631] [debug] 结束释放检测结果数组
[09-12 08:50:07.631] [debug] 开始释放列数据段
[09-12 08:50:07.631] [debug] 结束释放列数据段
[09-12 08:50:07.631] [debug] 已释放内部检测数据内存
[09-12 08:50:07.631] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.631] [debug] - 内存 - 目标跟踪完成: 使用 715.6 MB
[09-12 08:50:07.633] [debug] =====开始目标检测=====
[09-12 08:50:07.633] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.642] [debug] 检测到 1 个目标中心点
[09-12 08:50:07.642] [debug] 开始提取列数据段，共 1 个中心点
[09-12 08:50:07.642] [debug] 检测点[0]: 帧号=2462, 列=1792, 行=565, 段长度=256
[09-12 08:50:07.642] [debug] 目标检测耗时(ms): 预处理:3 推理:2 后处理:2 提取:0 总:7 (FPS:142.86)
[09-12 08:50:07.642] [debug] =====开始目标跟踪======
[09-12 08:50:07.642] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 08:50:07.642] [debug] 处理检测点[0]: 列=1792, 行=565
[09-12 08:50:07.642] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.642] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.642] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.642] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.643] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 08:50:07.643] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 08:50:07.647] [debug] 跟踪结果数量: 0
[09-12 08:50:07.647] [debug] 开始释放检测结果数组
[09-12 08:50:07.647] [debug] 结束释放检测结果数组
[09-12 08:50:07.647] [debug] 开始释放列数据段
[09-12 08:50:07.647] [debug] 结束释放列数据段
[09-12 08:50:07.647] [debug] 已释放内部检测数据内存
[09-12 08:50:07.647] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.647] [debug] - 内存 - 目标跟踪完成: 使用 557.8 MB
[09-12 08:50:07.647] [debug] =====开始目标检测=====
[09-12 08:50:07.647] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.659] [debug] 检测到 1 个目标中心点
[09-12 08:50:07.659] [debug] 开始提取列数据段，共 1 个中心点
[09-12 08:50:07.659] [debug] 检测点[0]: 帧号=2463, 列=1791, 行=564, 段长度=256
[09-12 08:50:07.659] [debug] 目标检测耗时(ms): 预处理:7 推理:2 后处理:1 提取:0 总:10 (FPS:100.00)
[09-12 08:50:07.659] [debug] =====开始目标跟踪======
[09-12 08:50:07.659] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 08:50:07.659] [debug] 处理检测点[0]: 列=1791, 行=564
[09-12 08:50:07.659] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.659] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.659] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.659] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.659] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 08:50:07.659] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 08:50:07.668] [debug] 跟踪结果数量: 1
[09-12 08:50:07.668] [info] Track[ 0] ID:  0 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:07.668] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:07.668] [debug] 开始释放检测结果数组
[09-12 08:50:07.668] [debug] 结束释放检测结果数组
[09-12 08:50:07.668] [debug] 开始释放列数据段
[09-12 08:50:07.668] [debug] 结束释放列数据段
[09-12 08:50:07.668] [debug] 已释放内部检测数据内存
[09-12 08:50:07.668] [debug] - 显存 - 目标跟踪完成: 使用 1021.8 MB
[09-12 08:50:07.668] [debug] - 内存 - 目标跟踪完成: 使用 461.8 MB
[09-12 08:50:07.980] [debug] =====开始目标检测=====
[09-12 08:50:07.980] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.990] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 08:50:07.990] [debug] =====开始目标跟踪======
[09-12 08:50:07.990] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.990] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.990] [debug] 跟踪结果数量: 0
[09-12 08:50:07.990] [info] Track[ 0] ID:  0 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:07.990] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:07.990] [debug] 开始释放检测结果数组
[09-12 08:50:07.990] [debug] 结束释放检测结果数组
[09-12 08:50:07.990] [debug] 开始释放列数据段
[09-12 08:50:07.990] [debug] 结束释放列数据段
[09-12 08:50:07.990] [debug] 已释放内部检测数据内存
[09-12 08:50:07.990] [debug] - 显存 - 目标跟踪完成: 使用 1021.8 MB
[09-12 08:50:07.990] [debug] - 内存 - 目标跟踪完成: 使用 469.9 MB
[09-12 08:50:08.382] [debug] =====开始目标检测=====
[09-12 08:50:08.382] [debug] 提取到 1024 个有效帧头
[09-12 08:50:08.382] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:08.382] [debug] =====开始目标跟踪======
[09-12 08:50:08.382] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:08.382] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:08.382] [debug] 跟踪结果数量: 0
[09-12 08:50:08.382] [info] Track[ 0] ID:  0 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:08.382] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:08.382] [debug] 开始释放检测结果数组
[09-12 08:50:08.382] [debug] 结束释放检测结果数组
[09-12 08:50:08.382] [debug] 开始释放列数据段
[09-12 08:50:08.382] [debug] 结束释放列数据段
[09-12 08:50:08.382] [debug] 已释放内部检测数据内存
[09-12 08:50:08.382] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:08.382] [debug] - 内存 - 目标跟踪完成: 使用 434.5 MB
[09-12 08:50:08.897] [debug] =====开始目标检测=====
[09-12 08:50:08.897] [debug] 提取到 1024 个有效帧头
[09-12 08:50:08.897] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:08.897] [debug] =====开始目标跟踪======
[09-12 08:50:08.897] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:08.897] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:08.897] [debug] 跟踪结果数量: 0
[09-12 08:50:08.897] [info] Track[ 0] ID:  0 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:08.897] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:08.897] [debug] 开始释放检测结果数组
[09-12 08:50:08.897] [debug] 结束释放检测结果数组
[09-12 08:50:08.897] [debug] 开始释放列数据段
[09-12 08:50:08.897] [debug] 结束释放列数据段
[09-12 08:50:08.897] [debug] 已释放内部检测数据内存
[09-12 08:50:08.897] [debug] - 显存 - 目标跟踪完成: 使用 1020.1 MB
[09-12 08:50:08.897] [debug] - 内存 - 目标跟踪完成: 使用 428.4 MB
[09-12 08:50:09.517] [debug] =====开始目标检测=====
[09-12 08:50:09.517] [debug] 提取到 1024 个有效帧头
[09-12 08:50:09.517] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:09.517] [debug] =====开始目标跟踪======
[09-12 08:50:09.518] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:09.518] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:09.518] [debug] 跟踪结果数量: 0
[09-12 08:50:09.518] [info] Track[ 0] ID:  0 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:09.518] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:09.518] [debug] 开始释放检测结果数组
[09-12 08:50:09.518] [debug] 结束释放检测结果数组
[09-12 08:50:09.518] [debug] 开始释放列数据段
[09-12 08:50:09.518] [debug] 结束释放列数据段
[09-12 08:50:09.518] [debug] 已释放内部检测数据内存
[09-12 08:50:09.518] [debug] - 显存 - 目标跟踪完成: 使用 1014.4 MB
[09-12 08:50:09.518] [debug] - 内存 - 目标跟踪完成: 使用 427.7 MB
[09-12 08:50:09.966] [debug] =====开始目标检测=====
[09-12 08:50:09.966] [debug] 提取到 1024 个有效帧头
[09-12 08:50:09.966] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:09.966] [debug] =====开始目标跟踪======
[09-12 08:50:09.966] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:09.966] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:09.966] [debug] 跟踪结果数量: 0
[09-12 08:50:09.966] [info] Track[ 0] ID:  0 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:35.665] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:30:35.665] [debug] =====算法库初始化开始=====
[09-12 09:30:35.665] [info] Initializing unified resource manager...
[09-12 09:30:35.666] [info] Initializing GPU resource manager...
[09-12 09:30:35.739] [debug] CUDA stream created: 0x5555560687f0
[09-12 09:30:35.739] [info] GPU resource manager initialized successfully
[09-12 09:30:35.739] [info] Initializing memory pool manager...
[09-12 09:30:35.739] [info] CUDA memory pool created with max size: 512MB
[09-12 09:30:35.739] [info] Host memory pool created with max size: 1024MB
[09-12 09:30:35.739] [info] Memory pool manager initialized successfully
[09-12 09:30:35.739] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 09:30:35.739] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 09:30:35.739] [debug] Initializing resource pool for length: 512
[09-12 09:30:35.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 09:30:35.740] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.740] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 09:30:35.741] [debug] Initializing resource pool for length: 256
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 09:30:35.741] [debug] Initializing resource pool for length: 128
[09-12 09:30:35.741] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 09:30:35.742] [debug] Initializing resource pool for length: 64
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 09:30:35.742] [info] All FFT resource pools initialized successfully
[09-12 09:30:35.742] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 09:30:35.742] [info] Unified resource manager initialized successfully
[09-12 09:30:35.742] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 09:30:35.743] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:30:35.743] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 09:30:35.758] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 09:30:35.758] [info] Engine info: 2 bindings
[09-12 09:30:35.758] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 09:30:35.758] [info]   Binding 1: output [1x1x512x1024]
[09-12 09:30:35.758] [info] 模型输入维度: [1x1024x1024x2]
[09-12 09:30:35.758] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 09:30:35.758] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 09:30:35.758] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 09:30:35.761] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 09:30:35.762] [debug] - 显存 - 算法初始化完成: 使用 953.9 MB
[09-12 09:30:35.762] [debug] - 内存 - 算法初始化完成: 使用 259.1 MB
[09-12 09:30:35.762] [debug] =====算法库初始化完成=====
[09-12 09:30:35.880] [debug] =====开始目标检测=====
[09-12 09:30:35.881] [debug] 提取到 1024 个有效帧头
[09-12 09:30:35.960] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 09:30:35.960] [debug] =====开始目标跟踪======
[09-12 09:30:35.960] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:35.960] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:35.960] [debug] 跟踪结果数量: 0
[09-12 09:30:35.960] [debug] 开始释放检测结果数组
[09-12 09:30:35.960] [debug] 结束释放检测结果数组
[09-12 09:30:35.960] [debug] 开始释放列数据段
[09-12 09:30:35.960] [debug] 结束释放列数据段
[09-12 09:30:35.960] [debug] 已释放内部检测数据内存
[09-12 09:30:35.962] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:35.962] [debug] - 内存 - 目标跟踪完成: 使用 482.6 MB
[09-12 09:30:36.000] [debug] =====开始目标检测=====
[09-12 09:30:36.000] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.012] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 09:30:36.012] [debug] =====开始目标跟踪======
[09-12 09:30:36.012] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.012] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.012] [debug] 跟踪结果数量: 0
[09-12 09:30:36.012] [debug] 开始释放检测结果数组
[09-12 09:30:36.012] [debug] 结束释放检测结果数组
[09-12 09:30:36.012] [debug] 开始释放列数据段
[09-12 09:30:36.012] [debug] 结束释放列数据段
[09-12 09:30:36.012] [debug] 已释放内部检测数据内存
[09-12 09:30:36.012] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.013] [debug] - 内存 - 目标跟踪完成: 使用 454.8 MB
[09-12 09:30:36.116] [debug] =====开始目标检测=====
[09-12 09:30:36.116] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.124] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 09:30:36.124] [debug] =====开始目标跟踪======
[09-12 09:30:36.124] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.124] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.124] [debug] 跟踪结果数量: 0
[09-12 09:30:36.124] [debug] 开始释放检测结果数组
[09-12 09:30:36.124] [debug] 结束释放检测结果数组
[09-12 09:30:36.124] [debug] 开始释放列数据段
[09-12 09:30:36.124] [debug] 结束释放列数据段
[09-12 09:30:36.124] [debug] 已释放内部检测数据内存
[09-12 09:30:36.124] [debug] - 显存 - 目标跟踪完成: 使用 955.2 MB
[09-12 09:30:36.124] [debug] - 内存 - 目标跟踪完成: 使用 477.9 MB
[09-12 09:30:36.236] [debug] =====开始目标检测=====
[09-12 09:30:36.236] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.247] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 09:30:36.247] [debug] =====开始目标跟踪======
[09-12 09:30:36.247] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.247] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.247] [debug] 跟踪结果数量: 0
[09-12 09:30:36.247] [debug] 开始释放检测结果数组
[09-12 09:30:36.247] [debug] 结束释放检测结果数组
[09-12 09:30:36.247] [debug] 开始释放列数据段
[09-12 09:30:36.247] [debug] 结束释放列数据段
[09-12 09:30:36.247] [debug] 已释放内部检测数据内存
[09-12 09:30:36.247] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.247] [debug] - 内存 - 目标跟踪完成: 使用 455.0 MB
[09-12 09:30:36.355] [debug] =====开始目标检测=====
[09-12 09:30:36.355] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.364] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 09:30:36.364] [debug] =====开始目标跟踪======
[09-12 09:30:36.364] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.364] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.364] [debug] 跟踪结果数量: 0
[09-12 09:30:36.364] [debug] 开始释放检测结果数组
[09-12 09:30:36.364] [debug] 结束释放检测结果数组
[09-12 09:30:36.364] [debug] 开始释放列数据段
[09-12 09:30:36.364] [debug] 结束释放列数据段
[09-12 09:30:36.364] [debug] 已释放内部检测数据内存
[09-12 09:30:36.364] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.364] [debug] - 内存 - 目标跟踪完成: 使用 452.0 MB
[09-12 09:30:36.476] [debug] =====开始目标检测=====
[09-12 09:30:36.476] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.486] [debug] 检测到 1 个目标中心点
[09-12 09:30:36.486] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:30:36.486] [debug] 检测点[0]: 帧号=2462, 列=1792, 行=565, 段长度=256
[09-12 09:30:36.486] [debug] 目标检测耗时(ms): 预处理:4 推理:2 后处理:3 提取:0 总:9 (FPS:111.11)
[09-12 09:30:36.488] [debug] =====开始目标跟踪======
[09-12 09:30:36.488] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:30:36.488] [debug] 处理检测点[0]: 列=1792, 行=565
[09-12 09:30:36.488] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.488] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.488] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.488] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.488] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:30:36.488] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 09:30:36.492] [debug] 跟踪结果数量: 0
[09-12 09:30:36.492] [debug] 开始释放检测结果数组
[09-12 09:30:36.492] [debug] 结束释放检测结果数组
[09-12 09:30:36.492] [debug] 开始释放列数据段
[09-12 09:30:36.492] [debug] 结束释放列数据段
[09-12 09:30:36.492] [debug] 已释放内部检测数据内存
[09-12 09:30:36.492] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.492] [debug] - 内存 - 目标跟踪完成: 使用 397.4 MB
[09-12 09:30:36.604] [debug] =====开始目标检测=====
[09-12 09:30:36.604] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.614] [debug] 检测到 1 个目标中心点
[09-12 09:30:36.614] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:30:36.614] [debug] 检测点[0]: 帧号=2463, 列=1791, 行=564, 段长度=256
[09-12 09:30:36.614] [debug] 目标检测耗时(ms): 预处理:3 推理:2 后处理:4 提取:0 总:9 (FPS:111.11)
[09-12 09:30:36.615] [debug] =====开始目标跟踪======
[09-12 09:30:36.615] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:30:36.615] [debug] 处理检测点[0]: 列=1791, 行=564
[09-12 09:30:36.615] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.615] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.615] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.615] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.615] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:30:36.615] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 09:30:36.618] [debug] 跟踪结果数量: 1
[09-12 09:30:36.618] [info] Track[ 0] ID:  0 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.618] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.618] [debug] 开始释放检测结果数组
[09-12 09:30:36.618] [debug] 结束释放检测结果数组
[09-12 09:30:36.618] [debug] 开始释放列数据段
[09-12 09:30:36.618] [debug] 结束释放列数据段
[09-12 09:30:36.618] [debug] 已释放内部检测数据内存
[09-12 09:30:36.618] [debug] - 显存 - 目标跟踪完成: 使用 953.0 MB
[09-12 09:30:36.618] [debug] - 内存 - 目标跟踪完成: 使用 449.2 MB
[09-12 09:30:36.727] [debug] =====开始目标检测=====
[09-12 09:30:36.727] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.737] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 09:30:36.738] [debug] =====开始目标跟踪======
[09-12 09:30:36.738] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.738] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.738] [debug] 跟踪结果数量: 0
[09-12 09:30:36.738] [info] Track[ 0] ID:  0 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.738] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.738] [debug] 开始释放检测结果数组
[09-12 09:30:36.738] [debug] 结束释放检测结果数组
[09-12 09:30:36.738] [debug] 开始释放列数据段
[09-12 09:30:36.738] [debug] 结束释放列数据段
[09-12 09:30:36.738] [debug] 已释放内部检测数据内存
[09-12 09:30:36.739] [debug] - 显存 - 目标跟踪完成: 使用 952.9 MB
[09-12 09:30:36.739] [debug] - 内存 - 目标跟踪完成: 使用 455.5 MB
[09-12 09:30:36.842] [debug] =====开始目标检测=====
[09-12 09:30:36.842] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.842] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:36.842] [debug] =====开始目标跟踪======
[09-12 09:30:36.842] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.842] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.842] [debug] 跟踪结果数量: 0
[09-12 09:30:36.842] [info] Track[ 0] ID:  0 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.842] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.842] [debug] 开始释放检测结果数组
[09-12 09:30:36.842] [debug] 结束释放检测结果数组
[09-12 09:30:36.842] [debug] 开始释放列数据段
[09-12 09:30:36.842] [debug] 结束释放列数据段
[09-12 09:30:36.842] [debug] 已释放内部检测数据内存
[09-12 09:30:36.843] [debug] - 显存 - 目标跟踪完成: 使用 952.9 MB
[09-12 09:30:36.843] [debug] - 内存 - 目标跟踪完成: 使用 426.3 MB
[09-12 09:30:36.951] [debug] =====开始目标检测=====
[09-12 09:30:36.951] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.951] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:36.951] [debug] =====开始目标跟踪======
[09-12 09:30:36.951] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.951] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.951] [debug] 跟踪结果数量: 0
[09-12 09:30:36.951] [info] Track[ 0] ID:  0 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.951] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.951] [debug] 开始释放检测结果数组
[09-12 09:30:36.951] [debug] 结束释放检测结果数组
[09-12 09:30:36.951] [debug] 开始释放列数据段
[09-12 09:30:36.951] [debug] 结束释放列数据段
[09-12 09:30:36.951] [debug] 已释放内部检测数据内存
[09-12 09:30:36.951] [debug] - 显存 - 目标跟踪完成: 使用 952.9 MB
[09-12 09:30:36.951] [debug] - 内存 - 目标跟踪完成: 使用 450.8 MB
[09-12 09:30:37.058] [debug] =====开始目标检测=====
[09-12 09:30:37.058] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.058] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.058] [debug] =====开始目标跟踪======
[09-12 09:30:37.058] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.058] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.058] [debug] 跟踪结果数量: 0
[09-12 09:30:37.058] [info] Track[ 0] ID:  0 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.058] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.058] [debug] 开始释放检测结果数组
[09-12 09:30:37.058] [debug] 结束释放检测结果数组
[09-12 09:30:37.058] [debug] 开始释放列数据段
[09-12 09:30:37.058] [debug] 结束释放列数据段
[09-12 09:30:37.058] [debug] 已释放内部检测数据内存
[09-12 09:30:37.058] [debug] - 显存 - 目标跟踪完成: 使用 953.4 MB
[09-12 09:30:37.058] [debug] - 内存 - 目标跟踪完成: 使用 425.1 MB
[09-12 09:30:37.169] [debug] =====开始目标检测=====
[09-12 09:30:37.169] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.169] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.169] [debug] =====开始目标跟踪======
[09-12 09:30:37.169] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.169] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.169] [debug] 跟踪结果数量: 0
[09-12 09:30:37.169] [info] Track[ 0] ID:  0 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.169] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.169] [debug] 开始释放检测结果数组
[09-12 09:30:37.169] [debug] 结束释放检测结果数组
[09-12 09:30:37.169] [debug] 开始释放列数据段
[09-12 09:30:37.169] [debug] 结束释放列数据段
[09-12 09:30:37.169] [debug] 已释放内部检测数据内存
[09-12 09:30:37.169] [debug] - 显存 - 目标跟踪完成: 使用 953.4 MB
[09-12 09:30:37.169] [debug] - 内存 - 目标跟踪完成: 使用 434.6 MB
[09-12 09:30:37.283] [debug] =====开始目标检测=====
[09-12 09:30:37.283] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.283] [info] 方位角变化，角度过滤，跳过帧: 2469，角度: 44.15，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.283] [debug] =====开始目标跟踪======
[09-12 09:30:37.283] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.283] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.284] [debug] 跟踪结果数量: 0
[09-12 09:30:37.284] [info] Track[ 0] ID:  0 | Pos: ( 865.70,  428.36,   98.13) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.86 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.284] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.284] [debug] 开始释放检测结果数组
[09-12 09:30:37.284] [debug] 结束释放检测结果数组
[09-12 09:30:37.284] [debug] 开始释放列数据段
[09-12 09:30:37.284] [debug] 结束释放列数据段
[09-12 09:30:37.284] [debug] 已释放内部检测数据内存
[09-12 09:30:37.284] [debug] - 显存 - 目标跟踪完成: 使用 954.4 MB
[09-12 09:30:37.284] [debug] - 内存 - 目标跟踪完成: 使用 425.5 MB
[09-12 09:30:37.568] [debug] =====开始目标检测=====
[09-12 09:30:37.568] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.568] [info] 方位角变化，角度过滤，跳过帧: 2470，角度: 47.25，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.568] [debug] =====开始目标跟踪======
[09-12 09:30:37.568] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.568] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.568] [debug] 跟踪结果数量: 0
[09-12 09:30:37.568] [info] Track[ 0] ID:  0 | Pos: ( 866.13,  428.57,   98.18) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.33 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.568] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.568] [debug] 开始释放检测结果数组
[09-12 09:30:37.568] [debug] 结束释放检测结果数组
[09-12 09:30:37.568] [debug] 开始释放列数据段
[09-12 09:30:37.568] [debug] 结束释放列数据段
[09-12 09:30:37.568] [debug] 已释放内部检测数据内存
[09-12 09:30:37.568] [debug] - 显存 - 目标跟踪完成: 使用 954.6 MB
[09-12 09:30:37.568] [debug] - 内存 - 目标跟踪完成: 使用 434.2 MB
[09-12 09:30:37.767] [debug] =====开始目标检测=====
[09-12 09:30:37.767] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.767] [info] 方位角变化，角度过滤，跳过帧: 2471，角度: 50.31，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.767] [debug] =====开始目标跟踪======
[09-12 09:30:37.767] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.767] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.767] [debug] 跟踪结果数量: 0
[09-12 09:30:37.767] [info] Track[ 0] ID:  0 | Pos: ( 866.55,  428.78,   98.23) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.81 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.767] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.767] [debug] 开始释放检测结果数组
[09-12 09:30:37.767] [debug] 结束释放检测结果数组
[09-12 09:30:37.767] [debug] 开始释放列数据段
[09-12 09:30:37.767] [debug] 结束释放列数据段
[09-12 09:30:37.767] [debug] 已释放内部检测数据内存
[09-12 09:30:37.767] [debug] - 显存 - 目标跟踪完成: 使用 954.4 MB
[09-12 09:30:37.767] [debug] - 内存 - 目标跟踪完成: 使用 434.4 MB
[09-12 09:30:38.076] [debug] =====开始目标检测=====
[09-12 09:30:38.076] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.076] [info] 方位角变化，角度过滤，跳过帧: 2472，角度: 53.39，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:38.076] [debug] =====开始目标跟踪======
[09-12 09:30:38.076] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.076] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.076] [debug] 跟踪结果数量: 0
[09-12 09:30:38.076] [info] Track[ 0] ID:  0 | Pos: ( 866.97,  428.99,   98.28) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.29 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.076] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.076] [debug] 开始释放检测结果数组
[09-12 09:30:38.076] [debug] 结束释放检测结果数组
[09-12 09:30:38.076] [debug] 开始释放列数据段
[09-12 09:30:38.076] [debug] 结束释放列数据段
[09-12 09:30:38.076] [debug] 已释放内部检测数据内存
[09-12 09:30:38.076] [debug] - 显存 - 目标跟踪完成: 使用 954.5 MB
[09-12 09:30:38.076] [debug] - 内存 - 目标跟踪完成: 使用 424.5 MB
[09-12 09:30:38.271] [debug] =====开始目标检测=====
[09-12 09:30:38.271] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.271] [info] 方位角变化，角度过滤，跳过帧: 2473，角度: 56.49，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:38.271] [debug] =====开始目标跟踪======
[09-12 09:30:38.271] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.271] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.272] [debug] 跟踪结果数量: 0
[09-12 09:30:38.272] [info] Track[ 0] ID:  0 | Pos: ( 867.40,  429.20,   98.33) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.76 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.272] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.272] [debug] 开始释放检测结果数组
[09-12 09:30:38.272] [debug] 结束释放检测结果数组
[09-12 09:30:38.272] [debug] 开始释放列数据段
[09-12 09:30:38.272] [debug] 结束释放列数据段
[09-12 09:30:38.272] [debug] 已释放内部检测数据内存
[09-12 09:30:38.272] [debug] - 显存 - 目标跟踪完成: 使用 955.2 MB
[09-12 09:30:38.272] [debug] - 内存 - 目标跟踪完成: 使用 434.4 MB
[09-12 09:30:38.502] [debug] =====开始目标检测=====
[09-12 09:30:38.502] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.513] [warning] 无效帧:2572 模型没有检测到目标，跳过处理
[09-12 09:30:38.513] [debug] =====开始目标跟踪======
[09-12 09:30:38.513] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.513] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.514] [debug] 跟踪结果数量: 0
[09-12 09:30:38.514] [info] Track[ 0] ID:  0 | Pos: ( 867.82,  429.41,   98.38) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.24 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.514] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.514] [debug] 开始释放检测结果数组
[09-12 09:30:38.514] [debug] 结束释放检测结果数组
[09-12 09:30:38.514] [debug] 开始释放列数据段
[09-12 09:30:38.514] [debug] 结束释放列数据段
[09-12 09:30:38.514] [debug] 已释放内部检测数据内存
[09-12 09:30:38.514] [debug] - 显存 - 目标跟踪完成: 使用 955.5 MB
[09-12 09:30:38.514] [debug] - 内存 - 目标跟踪完成: 使用 455.7 MB
[09-12 09:30:38.753] [debug] =====开始目标检测=====
[09-12 09:30:38.754] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.763] [warning] 无效帧:2573 模型没有检测到目标，跳过处理
[09-12 09:30:38.763] [debug] =====开始目标跟踪======
[09-12 09:30:38.763] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.763] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.763] [debug] 跟踪结果数量: 0
[09-12 09:30:38.763] [info] Track[ 0] ID:  0 | Pos: ( 868.25,  429.62,   98.42) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.71 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.763] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.763] [debug] 开始释放检测结果数组
[09-12 09:30:38.763] [debug] 结束释放检测结果数组
[09-12 09:30:38.763] [debug] 开始释放列数据段
[09-12 09:30:38.763] [debug] 结束释放列数据段
[09-12 09:30:38.763] [debug] 已释放内部检测数据内存
[09-12 09:30:38.763] [debug] - 显存 - 目标跟踪完成: 使用 955.3 MB
[09-12 09:30:38.763] [debug] - 内存 - 目标跟踪完成: 使用 455.8 MB
[09-12 09:30:38.961] [debug] =====开始目标检测=====
[09-12 09:30:38.961] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.972] [warning] 无效帧:2574 模型没有检测到目标，跳过处理
[09-12 09:30:38.972] [debug] =====开始目标跟踪======
[09-12 09:30:38.972] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.972] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.972] [debug] 跟踪结果数量: 0
[09-12 09:30:38.972] [info] Track[ 0] ID:  0 | Pos: ( 868.67,  429.84,   98.47) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.19 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.972] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.972] [debug] 开始释放检测结果数组
[09-12 09:30:38.972] [debug] 结束释放检测结果数组
[09-12 09:30:38.972] [debug] 开始释放列数据段
[09-12 09:30:38.972] [debug] 结束释放列数据段
[09-12 09:30:38.972] [debug] 已释放内部检测数据内存
[09-12 09:30:38.972] [debug] - 显存 - 目标跟踪完成: 使用 955.4 MB
[09-12 09:30:38.972] [debug] - 内存 - 目标跟踪完成: 使用 456.0 MB
[09-12 09:30:39.919] [debug] =====开始目标检测=====
[09-12 09:30:39.919] [debug] 提取到 1024 个有效帧头
[09-12 09:30:39.931] [warning] 无效帧:2575 模型没有检测到目标，跳过处理
[09-12 09:30:39.931] [debug] =====开始目标跟踪======
[09-12 09:30:39.931] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:39.931] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:39.931] [debug] 跟踪结果数量: 0
[09-12 09:30:39.931] [info] Track[ 0] ID:  0 | Pos: ( 869.10,  430.05,   98.52) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.67 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:39.931] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:39.931] [debug] 开始释放检测结果数组
[09-12 09:30:39.931] [debug] 结束释放检测结果数组
[09-12 09:30:39.931] [debug] 开始释放列数据段
[09-12 09:30:39.931] [debug] 结束释放列数据段
[09-12 09:30:39.931] [debug] 已释放内部检测数据内存
[09-12 09:30:39.931] [debug] - 显存 - 目标跟踪完成: 使用 954.2 MB
[09-12 09:30:39.931] [debug] - 内存 - 目标跟踪完成: 使用 486.1 MB
[09-12 09:30:40.347] [debug] =====开始目标检测=====
[09-12 09:30:40.348] [debug] 提取到 1024 个有效帧头
[09-12 09:30:40.357] [warning] 无效帧:2576 模型没有检测到目标，跳过处理
[09-12 09:30:40.357] [debug] =====开始目标跟踪======
[09-12 09:30:40.357] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:40.357] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:40.358] [debug] 跟踪结果数量: 0
[09-12 09:30:40.358] [info] Track[ 0] ID:  0 | Pos: ( 869.52,  430.26,   98.57) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.14 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
